<template>
  <div class="publish-container">
    <div class="publish-header">
      <div class="container">
        <h1>发布 MCP 服务</h1>
        <p>分享您的 Model Context Protocol 服务给社区</p>
      </div>
    </div>

    <div class="publish-content">
      <div class="container">
        <div class="publish-form-container">
          <form @submit.prevent="handleSubmit" class="publish-form">
            <div class="form-section">
              <h2>基本信息</h2>
              
              <div class="form-group">
                <label for="name">服务名称 *</label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  required
                  placeholder="请输入服务名称"
                />
              </div>

              <div class="form-group">
                <label for="description">服务描述 *</label>
                <textarea
                  id="description"
                  v-model="form.description"
                  required
                  rows="4"
                  placeholder="请详细描述您的服务功能和用途"
                ></textarea>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="category">分类 *</label>
                  <select id="category" v-model="form.category" required>
                    <option value="">请选择分类</option>
                    <option value="ai">AI 助手</option>
                    <option value="data">数据处理</option>
                    <option value="web">网络服务</option>
                    <option value="tool">工具类</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="version">版本号 *</label>
                  <input
                    id="version"
                    v-model="form.version"
                    type="text"
                    required
                    placeholder="例如: 1.0.0"
                  />
                </div>
              </div>
            </div>

            <div class="form-section">
              <h2>技术信息</h2>
              
              <div class="form-group">
                <label for="repository">代码仓库 URL</label>
                <input
                  id="repository"
                  v-model="form.repository"
                  type="url"
                  placeholder="https://github.com/username/repo"
                />
              </div>

              <div class="form-group">
                <label for="documentation">文档链接</label>
                <input
                  id="documentation"
                  v-model="form.documentation"
                  type="url"
                  placeholder="https://docs.example.com"
                />
              </div>

              <div class="form-group">
                <label for="tags">标签</label>
                <input
                  id="tags"
                  v-model="form.tags"
                  type="text"
                  placeholder="用逗号分隔，例如: ai, nlp, chatbot"
                />
                <small>用逗号分隔多个标签</small>
              </div>
            </div>

            <div class="form-section">
              <h2>MCP 配置</h2>
              
              <div class="form-group">
                <label for="mcpConfig">MCP 配置文件 *</label>
                <textarea
                  id="mcpConfig"
                  v-model="form.mcpConfig"
                  required
                  rows="8"
                  placeholder="请粘贴您的 MCP 配置 JSON"
                ></textarea>
                <small>请提供完整的 MCP 服务配置信息</small>
              </div>
            </div>

            <div v-if="error" class="error-message">
              {{ error }}
            </div>

            <div v-if="success" class="success-message">
              {{ success }}
            </div>

            <div class="form-actions">
              <button type="button" @click="saveDraft" class="btn btn-secondary" :disabled="loading">
                保存草稿
              </button>
              <button type="submit" class="btn btn-primary" :disabled="loading">
                <span v-if="loading" class="spinner"></span>
                {{ loading ? '发布中...' : '发布服务' }}
              </button>
            </div>
          </form>

          <div class="publish-sidebar">
            <div class="sidebar-card">
              <h3>发布指南</h3>
              <ul>
                <li>确保服务名称简洁明了</li>
                <li>提供详细的功能描述</li>
                <li>选择合适的分类标签</li>
                <li>提供完整的 MCP 配置</li>
                <li>添加代码仓库链接</li>
              </ul>
            </div>

            <div class="sidebar-card">
              <h3>MCP 配置示例</h3>
              <pre><code>{
  "name": "my-service",
  "version": "1.0.0",
  "description": "My MCP service",
  "main": "index.js",
  "capabilities": {
    "tools": true,
    "resources": true
  }
}</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'PublishView',
  data() {
    return {
      loading: false,
      error: '',
      success: '',
      form: {
        name: '',
        description: '',
        category: '',
        version: '',
        repository: '',
        documentation: '',
        tags: '',
        mcpConfig: ''
      }
    }
  },
  async mounted() {
    const authStore = useAuthStore()
    if (!authStore.isLoggedIn) {
      this.$router.push('/auth')
    }
  },
  methods: {
    async handleSubmit() {
      this.error = ''
      this.success = ''
      this.loading = true

      try {
        // Validate MCP config
        try {
          JSON.parse(this.form.mcpConfig)
        } catch (e) {
          throw new Error('MCP 配置格式不正确，请检查 JSON 格式')
        }

        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // Mock successful publish
        this.success = '服务发布成功！正在审核中，审核通过后将在服务广场显示。'
        this.resetForm()
        
        // Redirect after success
        setTimeout(() => {
          this.$router.push('/')
        }, 3000)
        
      } catch (error) {
        this.error = error.message || '发布失败，请重试'
      } finally {
        this.loading = false
      }
    },

    async saveDraft() {
      this.error = ''
      this.success = ''
      
      try {
        // Mock save draft
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.success = '草稿已保存'
        
        setTimeout(() => {
          this.success = ''
        }, 3000)
      } catch (error) {
        this.error = '保存失败，请重试'
      }
    },

    resetForm() {
      this.form = {
        name: '',
        description: '',
        category: '',
        version: '',
        repository: '',
        documentation: '',
        tags: '',
        mcpConfig: ''
      }
    }
  }
}
</script>

<style scoped>
.publish-container {
  min-height: 100vh;
  background: #f8fafc;
}

.publish-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
}

.publish-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.publish-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.publish-content {
  padding: 3rem 0;
}

.publish-form-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: start;
}

.publish-form {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #4f46e5;
}

.form-group small {
  display: block;
  margin-top: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #fecaca;
}

.success-message {
  background: #f0fdf4;
  color: #16a34a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #bbf7d0;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.publish-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sidebar-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.sidebar-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-card li {
  padding: 0.5rem 0;
  color: #4b5563;
  border-bottom: 1px solid #f3f4f6;
}

.sidebar-card li:last-child {
  border-bottom: none;
}

.sidebar-card li:before {
  content: "✓";
  color: #10b981;
  font-weight: bold;
  margin-right: 0.5rem;
}

.sidebar-card pre {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
  font-size: 0.875rem;
  overflow-x: auto;
  margin: 0;
}

.sidebar-card code {
  color: #374151;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

@media (max-width: 768px) {
  .publish-form-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
