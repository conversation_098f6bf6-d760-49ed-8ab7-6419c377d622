import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated && state.user !== null,
    currentUser: (state) => state.user,
    userToken: (state) => state.token
  },

  actions: {
    async login(credentials) {
      this.loading = true
      try {
        // Mock API call - replace with actual API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock successful login
        if (credentials.username === 'admin' && credentials.password === 'password') {
          const mockUser = {
            id: 1,
            username: credentials.username,
            email: '<EMAIL>',
            avatar: '/api/placeholder/64/64'
          }
          
          const mockToken = 'mock-jwt-token-' + Date.now()
          
          this.user = mockUser
          this.token = mockToken
          this.isAuthenticated = true
          
          // Store in localStorage
          localStorage.setItem('auth_token', mockToken)
          localStorage.setItem('auth_user', JSON.stringify(mockUser))
          
          return { user: mockUser, token: mockToken }
        } else {
          throw new Error('用户名或密码错误')
        }
      } catch (error) {
        this.user = null
        this.token = null
        this.isAuthenticated = false
        throw error
      } finally {
        this.loading = false
      }
    },

    async register(userData) {
      this.loading = true
      try {
        // Mock API call - replace with actual API
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // Mock successful registration
        const mockUser = {
          id: Date.now(),
          username: userData.username,
          email: userData.email,
          avatar: '/api/placeholder/64/64'
        }
        
        const mockToken = 'mock-jwt-token-' + Date.now()
        
        this.user = mockUser
        this.token = mockToken
        this.isAuthenticated = true
        
        // Store in localStorage
        localStorage.setItem('auth_token', mockToken)
        localStorage.setItem('auth_user', JSON.stringify(mockUser))
        
        return { user: mockUser, token: mockToken }
      } catch (error) {
        this.user = null
        this.token = null
        this.isAuthenticated = false
        throw error
      } finally {
        this.loading = false
      }
    },

    async logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      
      // Clear localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
    },

    async checkAuth() {
      const token = localStorage.getItem('auth_token')
      const userStr = localStorage.getItem('auth_user')
      
      if (token && userStr) {
        try {
          const user = JSON.parse(userStr)
          this.user = user
          this.token = token
          this.isAuthenticated = true
          return true
        } catch (error) {
          console.error('Failed to parse stored user data:', error)
          this.logout()
          return false
        }
      }
      
      return false
    },

    async refreshToken() {
      // Mock token refresh - replace with actual API
      if (this.token) {
        try {
          await new Promise(resolve => setTimeout(resolve, 500))
          const newToken = 'refreshed-token-' + Date.now()
          this.token = newToken
          localStorage.setItem('auth_token', newToken)
          return newToken
        } catch (error) {
          console.error('Token refresh failed:', error)
          this.logout()
          throw error
        }
      }
    }
  }
})
